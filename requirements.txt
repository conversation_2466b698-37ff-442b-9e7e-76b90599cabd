# The order of packages is significant, because pip processes them in the order
# of appearance. Changing the order has an impact on the overall integration
# process, which may cause wedges in the gate later.
pbr!=2.1.0,>=2.0.0 # Apache-2.0
SQLAlchemy!=1.1.5,!=1.1.6,!=1.1.7,!=1.1.8,>=1.0.10 # MIT
eventlet!=0.18.3,!=0.20.1,>=0.18.2 # MIT
keystonemiddleware>=4.17.0 # Apache-2.0
Routes>=2.3.1 # MIT
WebOb>=1.7.1 # MIT
PasteDeploy>=1.5.0 # MIT
Paste>=2.0.2 # MIT
sqlalchemy-migrate>=0.11.0 # Apache-2.0
netaddr>=0.7.18 # BSD
httplib2>=0.9.1 # MIT
lxml!=3.7.0,>=3.4.1 # BSD
passlib>=1.7.0 # BSD
python-heatclient>=1.10.0 # Apache-2.0
python-novaclient>=9.1.0 # Apache-2.0
python-cinderclient>=3.3.0 # Apache-2.0
python-keystoneclient>=3.8.0 # Apache-2.0
python-swiftclient>=3.2.0 # Apache-2.0
python-designateclient>=2.7.0 # Apache-2.0
python-neutronclient>=6.7.0 # Apache-2.0
python-glanceclient>=2.8.0 # Apache-2.0
python-troveclient>=2.2.0 # Apache-2.0
iso8601>=0.1.11 # MIT
jsonschema>=3.2.0 # MIT
Jinja2>=2.10 # BSD License (3 clause)
pexpect!=3.3,>=3.1 # ISC License
oslo.config>=6.8.0 # Apache-2.0
oslo.context>=4.0.0 # Apache-2.0
oslo.i18n>=3.15.3 # Apache-2.0
oslo.middleware>=3.31.0 # Apache-2.0
oslo.serialization!=2.19.1,>=2.18.0 # Apache-2.0
oslo.service!=1.28.1,>=1.24.0 # Apache-2.0
oslo.upgradecheck>=1.3.0 # Apache-2.0
oslo.utils>=3.40.0 # Apache-2.0
oslo.concurrency>=3.26.0 # Apache-2.0
PyMySQL>=0.7.6 # MIT License
stevedore>=1.20.0 # Apache-2.0
oslo.messaging>=14.1.0 # Apache-2.0
osprofiler>=1.4.0 # Apache-2.0
oslo.log>=3.36.0 # Apache-2.0
oslo.db>=4.27.0 # Apache-2.0
xmltodict>=0.10.1 # MIT
cryptography>=2.1.4  # BSD/Apache-2.0
oslo.policy>=3.6.0 # Apache-2.0
diskimage-builder!=1.6.0,!=1.7.0,!=1.7.1,>=1.1.2 # Apache-2.0
docker>=4.2.0 # Apache-2.0
psycopg2-binary>=2.6.2 # LGPL/ZPL
semantic-version>=2.7.0 # BSD
oslo.cache>=1.26.0 # Apache-2.0

# for trove network driver
Flask>=2.2.3 # BSD
pyroute2>=0.7.7;sys_platform!='win32' # Apache-2.0 (+ dual licensed GPL2)
gunicorn>=20.1.0 # MIT