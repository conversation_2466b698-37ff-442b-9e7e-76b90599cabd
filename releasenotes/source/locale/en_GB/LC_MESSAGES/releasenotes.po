# <PERSON><PERSON> <<EMAIL>>, 2020. #zanata
# <PERSON><PERSON> <<EMAIL>>, 2022. #zanata
# <PERSON><PERSON> <<EMAIL>>, 2023. #zanata
msgid ""
msgstr ""
"Project-Id-Version: trove\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-22 13:32+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2023-07-28 12:17+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom)\n"
"Language: en_GB\n"
"X-Generator: Zanata 4.3.3\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"

msgid "10.0.0"
msgstr "10.0.0"

msgid "11.0.0"
msgstr "11.0.0"

msgid "12.0.0"
msgstr "12.0.0"

msgid "13.0.0"
msgstr "13.0.0"

msgid "14.0.0"
msgstr "14.0.0"

msgid "14.0.0.0rc1"
msgstr "14.0.0.0rc1"

msgid "14.1.0"
msgstr "14.1.0"

msgid "15.0.0"
msgstr "15.0.0"

msgid "15.0.0-16"
msgstr "15.0.0-16"

msgid "16.0.0"
msgstr "16.0.0"

msgid "16.0.1"
msgstr "16.0.1"

msgid "16.0.1-2"
msgstr "16.0.1-2"

msgid "18.0.0"
msgstr "18.0.0"

msgid "19.0.0"
msgstr "19.0.0"

msgid "2023.1 Series Release Notes"
msgstr "2023.1 Series Release Notes"

msgid "5.0.0"
msgstr "5.0.0"

msgid "5.0.1"
msgstr "5.0.1"

msgid "5.1.0"
msgstr "5.1.0"

msgid "5.1.1"
msgstr "5.1.1"

msgid "6.0.0"
msgstr "6.0.0"

msgid "7.0.0"
msgstr "7.0.0"

msgid "8.0.0"
msgstr "8.0.0"

msgid "9.0.0"
msgstr "9.0.0"

msgid "9.0.0-8"
msgstr "9.0.0-8"

msgid ""
"A locality flag was added to the trove ReST API to allow a user to specify "
"whether new replicas should be on the same hypervisor (affinity) or on "
"different hypervisors (anti-affinity)."
msgstr ""
"A locality flag was added to the Trove REST API to allow a user to specify "
"whether new replicas should be on the same hypervisor (affinity) or on "
"different hypervisors (anti-affinity)."

msgid ""
"A locality flag was added to the trove ReST API to allow a user to specify "
"whether the instances of a cluster should be on the same hypervisor "
"(affinity) or on different hypervisors (anti-affinity)."
msgstr ""
"A locality flag was added to the trove REST API to allow a user to specify "
"whether the instances of a cluster should be on the same hypervisor "
"(affinity) or on different hypervisors (anti-affinity)."

msgid ""
"A new database service status ``HEALTHY`` is introduced to indicate that the "
"service is responsive. ``HEALTHY`` is the final status after ``ACTIVE``."
msgstr ""
"A new database service status ``HEALTHY`` is introduced to indicate that the "
"service is responsive. ``HEALTHY`` is the final status after ``ACTIVE``."

msgid ""
"A new feature called 'module management' has been added to Trove. Users can "
"now create, update, list and delete modules. A module is a file that is "
"provided to Trove, and when a database instance is launched, that file is "
"deposited on the guest instance. This feature can be used for depositing "
"files like, for example, license files onto guest database instances."
msgstr ""
"A new feature called 'module management' has been added to Trove. Users can "
"now create, update, list and delete modules. A module is a file that is "
"provided to Trove, and when a database instance is launched, that file is "
"deposited on the guest instance. This feature can be used for depositing "
"files like, for example, licence files onto guest database instances."

msgid ""
"A new field named ``service_status_updated`` is added to the instance API "
"response which e.g. could be used to validate if the instance 'HEALTHY' "
"status is stale or not"
msgstr ""
"A new field named ``service_status_updated`` is added to the instance API "
"response which e.g. could be used to validate if the instance 'HEALTHY' "
"status is stale or not"

msgid ""
"Add 'max-prepared-stmt-cnt' to the list of valid values which can be used in "
"configuration groups for Mysql, Percona, MariaDB and PXC"
msgstr ""
"Add 'max-prepared-stmt-cnt' to the list of valid values which can be used in "
"configuration groups for MySQL, Percona, MariaDB and PXC"

msgid ""
"Add Compute ID (server_id) and Volume ID (volume_id) to trove show output "
"for admin users. Bug"
msgstr ""
"Add Compute ID (server_id) and Volume ID (volume_id) to Trove show output "
"for admin users. Bug"

msgid ""
"Add RBAC (role-based access control) enforcement on all trove APIs. Allows "
"to define a role-based access rule for every trove API call (rule "
"definitions are available in /etc/trove/policy.json)."
msgstr ""
"Add RBAC (role-based access control) enforcement on all trove APIs. Allows "
"to define a role-based access rule for every Trove API call (rule "
"definitions are available in /etc/trove/policy.json)."

msgid ""
"Add XFS disk format for database data volume, cloud admin can config 'ext3', "
"'ext4' or 'xfs' in ``volume_fstype`` option."
msgstr ""
"Add XFS disk format for database data volume, cloud admin can config 'ext3', "
"'ext4' or 'xfs' in ``volume_fstype`` option."

msgid "Add disk column in flavor-list Bug 1617987."
msgstr "Add disk column in flavour-list Bug 1617987."

msgid "Add ephemeral column in flavor-list (Bug 1617980)"
msgstr "Add ephemeral column in flavour-list (Bug 1617980)"

msgid ""
"Add icmp option for DB security group. if icmp=True, users will be allowed "
"to ping to DB instances."
msgstr ""
"Add icmp option for DB security group. if icmp=True, users will be allowed "
"to ping to DB instances."

msgid "Add support for cluster restart."
msgstr "Add support for cluster restart."

msgid "Add support for configuration group management for DB2 Express-C."
msgstr "Add support for configuration group management for DB2 Express-C."

msgid ""
"Add support for full online backup and restore for DB2 Express-C by enabling "
"archive logging."
msgstr ""
"Add support for full online backup and restore for DB2 Express-C by enabling "
"archive logging."

msgid "Add vCPUs column in flavor-list Bug 1261876."
msgstr "Add vCPUs column in flavour-list Bug 1261876."

msgid "Added CORS support."
msgstr "Added CORS support."

msgid ""
"Added MySQL 8 support, docker image openstacktrove/db-backup-mysql8.0 is "
"created to support backup and restore."
msgstr ""
"Added MySQL 8 support, docker image openstacktrove/db-backup-mysql8.0 is "
"created to support backup and restore."

msgid ""
"Added ``/instances/detail`` endpoint to fetch list of instances with details."
msgstr ""
"Added ``/instances/detail`` endpoint to fetch list of instances with details."

msgid ""
"Added ``replicas`` attribute for listing instances when the instance is the "
"master of the replication cluster."
msgstr ""
"Added ``replicas`` attribute for listing instances when the instance is the "
"master of the replication cluster."

msgid ""
"Added a config option ``enable_access_check`` (default True) to decide if "
"Trove should check the subnet of the user port is associated with a Neutron "
"router. This check is needed for creating public-facing instances and the "
"instance initialization. This check could be skipped When using Neutron "
"provider network."
msgstr ""
"Added a config option ``enable_access_check`` (default True) to decide if "
"Trove should check the subnet of the user port is associated with a Neutron "
"router. This check is needed for creating public-facing instances and the "
"instance initialization. This check could be skipped When using Neutron "
"provider network."

msgid ""
"Added a module driver for New Relics licenses. This allows activation of any "
"New Relic software that is installed on the image. Bug 1571711"
msgstr ""
"Added a module driver for New Relics licenses. This allows activation of any "
"New Relic software that is installed on the image. Bug 1571711"

msgid ""
"Added a new config option ``nova_keypair`` to specify an existing Nova "
"keypair name for the database instance creation, the cloud administrator is "
"responsible for the keypair management and configuration. It's recommended "
"to create Trove database instance in the admin project for security reasons, "
"so only the cloud administrator who has the private key can access the "
"database instance. With the keypair support, ssh keys are no longer injected "
"into Trove guest agent image at build time."
msgstr ""
"Added a new config option ``nova_keypair`` to specify an existing Nova "
"keypair name for the database instance creation, the cloud administrator is "
"responsible for the keypair management and configuration. It's recommended "
"to create Trove database instance in the admin project for security reasons, "
"so only the cloud administrator who has the private key can access the "
"database instance. With the keypair support, ssh keys are no longer injected "
"into Trove guest agent image at build time."

msgid ""
"Added a new field ``operating_status`` for the instance to show the actual "
"operational status of user's database. See https://docs.openstack.org/trove/"
"latest/user/instance-status.html for more information."
msgstr ""
"Added a new field ``operating_status`` for the instance to show the actual "
"operational status of the user's database. See https://docs.openstack.org/"
"trove/latest/user/instance-status.html for more information."

msgid ""
"Added a new field named ``addresses`` in the instance API response which "
"including the IP address and type, either 'private' or 'public'."
msgstr ""
"Added a new field named ``addresses`` in the instance API response which "
"including the IP address and type, either 'private' or 'public'."

msgid ""
"Added a periodic task for postgresql datastore to clean up the archived WAL "
"files. Added a check when creating incremental backups for postgresql. A new "
"container image ``openstacktrove/db-backup-postgresql:1.1.2`` is uploaded to "
"docker hub."
msgstr ""
"Added a periodic task for the PostgreSQL datastore to clean up the archived "
"WAL files. Added a check when creating incremental backups for PostgreSQL. A "
"new container image ``openstacktrove/db-backup-postgresql:1.1.2`` is "
"uploaded to docker hub."

msgid ""
"Added custom container registry configuration for trove guest agent, it's "
"now possible to use images in private registry rather than docker hub."
msgstr ""
"Added custom container registry configuration for Trove guest agent, it's "
"now possible to use images in the private registry rather than Docker hub."

msgid ""
"Added iptables-persistent package in the guest image element for diskimage-"
"builder. The Trove service administrator is able to perform iptables command "
"(leveraging cloud-init config) and persist the change."
msgstr ""
"Added iptables-persistent package in the guest image element for diskimage-"
"builder. The Trove service administrator is able to perform iptables command "
"(leveraging cloud-init config) and persist the change."

msgid "Added new tool ``trove-status upgrade check``."
msgstr "Added new tool ``trove-status upgrade check``."

msgid ""
"Added support for designate v2 api with a new dns driver. To use this driver "
"set dns_driver = trove.dns.designate.driver.DesignateDriverV2"
msgstr ""
"Added support for designate v2 API with a new DNS driver. To use this driver "
"set dns_driver = trove.dns.designate.driver.DesignateDriverV2"

msgid ""
"Added support to show and update the access configuration for the instance."
msgstr ""
"Added support to show and update the access configuration for the instance."

msgid ""
"Added the ability to associate datastore versions with volume types. This "
"enables operators to limit the volume types available when launching "
"datastores. The associations are set via the trove-manage tool commands "
"datastore_version_volume_type_add, datastore_version_volume_type_delete, and "
"datastore_version_volume_type_list. If a user attempts to create an instance "
"with a volume type that is not on the approved list for the specified "
"datastore version they will receive an error."
msgstr ""
"Added the ability to associate datastore versions with volume types. This "
"enables operators to limit the volume types available when launching "
"datastores. The associations are set via the trove-manage tool commands "
"datastore_version_volume_type_add, datastore_version_volume_type_delete, and "
"datastore_version_volume_type_list. If a user attempts to create an instance "
"with a volume type that is not on the approved list for the specified "
"datastore version they will receive an error."

msgid ""
"Added the ability to create the cinder volume in the same AZ as the nova "
"instance. Set ``enable_volume_az`` to True (defaults to False)"
msgstr ""
"Added the ability to create the Cinder volume in the same AZ as the Nova "
"instance. Set ``enable_volume_az`` to True (defaults to False)"

msgid ""
"Added the ability to quota on total amount of RAM in MB used per project. "
"Set ``quota.max_ram_per_tenant`` to enable. Default is -1 (unlimited) to be "
"backwards compatible. Existing installations will need to manually backfill "
"quote usage for this to work as expected."
msgstr ""
"Added the ability to quota on the total amount of RAM in MB used per "
"project. Set ``quota.max_ram_per_tenant`` to enable. Default is -1 "
"(unlimited) to be backwards compatible. Existing installations will need to "
"manually backfill quote usage for this to work as expected."

msgid "Adding the ability to root enable a pxc cluster."
msgstr "Adding the ability to root enable a pxc cluster."

msgid ""
"Additional Ceilometer notifications have been provided by Trove including "
"create, end, error notifications for all state-changing API calls."
msgstr ""
"Additional Ceilometer notifications have been provided by Trove including "
"create, end, error notifications for all state-changing API calls."

msgid ""
"Adds a region property to the instance model and table.  This is the first "
"step in multi-region support."
msgstr ""
"Adds a region property to the instance model and table.  This is the first "
"step in multi-region support."

msgid ""
"Adds new fields \"instance_ids\", which is supposed to contain ids of "
"cluster instances, in payloads of two cluster events - DBaaSClusterShrink "
"and DBaaSClusterGrow. Moreover, additional end notifications after growing "
"and shrinking cluster have been added. It allows better integration with "
"tools for monitoring resources usage."
msgstr ""
"Adds new fields \"instance_ids\", which is supposed to contain ids of "
"cluster instances, in payloads of two cluster events - DBaaSClusterShrink "
"and DBaaSClusterGrow. Moreover, additional end notifications after growing "
"and shrinking cluster have been added. It allows better integration with "
"tools for monitoring resources usage."

msgid ""
"Admin user can delete datastore if there are no instances or backups "
"associated."
msgstr ""
"Admin user can delete datastore if there are no instances or backups "
"associated."

msgid ""
"After upgrading the guestagent was in an inconsistent state. This became "
"apparent after restarting or resizing the instance after upgrading."
msgstr ""
"After upgrading the guest agent was in an inconsistent state. This became "
"apparent after restarting or resizing the instance after upgrading."

msgid "Allow specification of volume-type on cluster create. (Bug 1623005)"
msgstr "Allow specification of volume-type on cluster create. (Bug 1623005)"

msgid "An invalid module driver is now logged correctly. Bug 1579900"
msgstr "An invalid module driver is now logged correctly. Bug 1579900"

msgid ""
"Any 3rd party clients that are talking with Trove API to wait for ``status`` "
"should be notified to use ``operating_status`` instead."
msgstr ""
"Any 3rd party clients that are talking with Trove API to wait for ``status`` "
"should be notified to use ``operating_status`` instead."

msgid ""
"Any existing scripts that rely on the database instance ``ACTIVE`` status "
"should now rely on ``HEALTHY`` status."
msgstr ""
"Any existing scripts that rely on the database instance ``ACTIVE`` status "
"should now rely on ``HEALTHY`` status."

msgid ""
"Applying a module again will now relect the update name, type, datastore and "
"datastore_version values. Bug 1611525"
msgstr ""
"Applying a module again will now reflect the update name, type, datastore "
"and datastore_version values. Bug 1611525"

msgid ""
"Backups to Swift will now use Static Large Objects for larger backups. A new "
"configuration option 'backup_segment_max_size' can be set to adjust the "
"segment size of the SLO. Backups that are smaller than the segment size will "
"be uploaded as regular objects. This is an improvement over old Dynamic "
"Large Object implementation. Bug 1489997."
msgstr ""
"Backups to Swift will now use Static Large Objects for larger backups. A new "
"configuration option 'backup_segment_max_size' can be set to adjust the "
"segment size of the SLO. Backups that are smaller than the segment size will "
"be uploaded as regular objects. This is an improvement over old Dynamic "
"Large Object implementation. Bug 1489997."

msgid "Bug Fixes"
msgstr "Bug Fixes"

msgid ""
"Case where a new instance_modules record is written for each apply has been "
"fixed. This issue would have potentially made it impossible to delete a "
"module. Bug 1640010"
msgstr ""
"Case where a new instance_modules record is written for each apply has been "
"fixed. This issue would have potentially made it impossible to delete a "
"module. Bug 1640010"

msgid ""
"Changed the network mode of database container to \"bridge\" and exposed the "
"service ports. Cloud operator could adjust the iptables to restrict network "
"access from the database container to the outside. An example::"
msgstr ""
"Changed the network mode of the database container to \"bridge\" and exposed "
"the service ports. Cloud operator could adjust the iptables to restrict "
"network access from the database container to the outside. An example::"

msgid "Close the race condition window in user-list call. Closes-Bug 1617464"
msgstr "Close the race condition window in user-list call. Closes-Bug 1617464"

msgid ""
"Cloud administrator needs to create a Nova keypair and specify the keypair "
"name for config option ``nova_keypair``, the private key is used to ssh into "
"new database instances created. The previous private key is also needed to "
"ssh into the existing database instances."
msgstr ""
"Cloud administrator needs to create a Nova keypair and specify the keypair "
"name for config option ``nova_keypair``, the private key is used to ssh into "
"new database instances created. The previous private key is also needed to "
"ssh into the existing database instances."

msgid "Configuration show masks any password values."
msgstr "Configuration show masks any password values."

msgid "Current Series Release Notes"
msgstr "Current Series Release Notes"

msgid ""
"Database service (mysql and mariadb) is now running as docker container "
"inside the trove instance. The image is defined by ``docker_image`` config "
"option for each datastore."
msgstr ""
"Database service (MySQL and MariaDB) is now running as a Docker container "
"inside the trove instance. The image is defined by ``docker_image`` config "
"option for each datastore."

msgid "Deprecation Notes"
msgstr "Deprecation Notes"

msgid ""
"Do not remove MySQL root user on root-disable so that the proper status can "
"be reported on restore. Bug 1549600"
msgstr ""
"Do not remove MySQL root user on root-disable so that the proper status can "
"be reported on restore. Bug 1549600"

msgid "Dropping support for python 2.6"
msgstr "Dropping support for Python 2.6"

msgid "Enable database log retrieval on Cassandra instances."
msgstr "Enable database log retrieval on Cassandra instances."

msgid ""
"Errors that occur in Trove are now persisted in the database and are "
"returned in the standard 'show' command."
msgstr ""
"Errors that occur in Trove are now persisted in the database and are "
"returned in the standard 'show' command."

msgid ""
"Existing database services are not affected. However, in order for Trove to "
"communicate with trove guest agent, new guest image needs to be built and "
"existing trove instances need to be backed up and restored."
msgstr ""
"Existing database services are not affected. However, in order for Trove to "
"communicate with Trove guest agent, a new guest image needs to be built and "
"existing trove instances need to be backed up and restored."

msgid ""
"Filter ignored users in the original query before the result gets paginated "
"(like in list_databases)."
msgstr ""
"Filter ignored users in the original query before the result gets paginated "
"(like in list_databases)."

msgid ""
"Fix IniCodec to deserialize Python objects. This also brings it in line with "
"other codecs. guestagent_utils.to_bytes return the byte values as ints. See "
"bug 1599656"
msgstr ""
"Fix IniCodec to deserialize Python objects. This also brings it in line with "
"other codecs. guestagent_utils.to_bytes return the byte values as ints. See "
"bug 1599656"

msgid "Fix Postgresql promote (bug 1633515)."
msgstr "Fix Postgresql promote (bug 1633515)."

msgid ""
"Fix bug 1537986 which corrects the pagination in the mysql user list "
"command. When internal users (ignore_users) are eliminated from the list, "
"the pagination was not correctly handled."
msgstr ""
"Fix bug 1537986 which corrects the pagination in the MySQL user list "
"command. When internal users (ignore_users) are eliminated from the list, "
"the pagination was not correctly handled."

msgid ""
"Fix cluster creation error which caused by \"NameError: name 'common_glance' "
"is not defined\""
msgstr ""
"Fix cluster creation error caused by \"NameError: name 'common_glance' is "
"not defined\""

msgid ""
"Fix docker start failed in trove guest-agent when docker_insecure_registries "
"is not set."
msgstr ""
"Fix Docker start failed in Trove guest-agent when docker_insecure_registries "
"is not set."

msgid ""
"Fix guest-agent failed to start mysql-5.7 container due to the missing of "
"the configdir. note that we don't test mysql 5.7(aka.5.7.40) image in Trove "
"CI tests. `Stroy 2010543 <https://storyboard.openstack.org/#!/"
"story/2010543>`__"
msgstr ""
"Fix guest-agent failed to start MySQL-5.7 container due to the missing of "
"the configdir. note that we don't test MySQL 5.7(aka.5.7.40) image in Trove "
"CI tests. `Stroy 2010543 <https://storyboard.openstack.org/#!/"
"story/2010543>`__"

msgid ""
"Fix guest-agent failed to start postgres container due to execution of the "
"\"CREATE DATABASE\" statement within the context manager of psycopg library. "
"See the following for details `Stroy 2010761 <https://storyboard.openstack."
"org/#!/story/2010761>`__"
msgstr ""
"Fix guest-agent failed to start PostgreSQL container due to execution of the "
"\"CREATE DATABASE\" statement within the context manager of psycopg library. "
"See the following for details `Stroy 2010761 <https://storyboard.openstack."
"org/#!/story/2010761>`__"

msgid ""
"Fix guest-agent.conf is not generated in trove guest vm. `Stroy 2010231 "
"<https://storyboard.openstack.org/#!/story/2010231>`__"
msgstr ""
"Fix guest-agent.conf is not generated in Trove guest VM. `Stroy 2010231 "
"<https://storyboard.openstack.org/#!/story/2010231>`__"

msgid ""
"Fix missing request-id in Trove services logs. `Story 2010451 <https://"
"storyboard.openstack.org/#!/story/2010451>`__"
msgstr ""
"Fix missing request-id in Trove services logs. `Story 2010451 <https://"
"storyboard.openstack.org/#!/story/2010451>`__"

msgid ""
"Fix mysql instance permission issue when restoring a backup. `Stroy 2010467 "
"<https://storyboard.openstack.org/#!/story/2010467>`__"
msgstr ""
"Fix MySQL instance permission issue when restoring a backup. `Stroy 2010467 "
"<https://storyboard.openstack.org/#!/story/2010467>`__"

msgid ""
"Fix race condition in cluster-show that returned erroneous not found error. "
"Bug 1643002"
msgstr ""
"Fix race condition in cluster-show that returned erroneous not found error. "
"Bug 1643002"

msgid ""
"Fixed a race condition that instance becomes ERROR when Trove is handling "
"creating and deleting at the same time."
msgstr ""
"Fixed a race condition that instance becomes ERROR when Trove is handling "
"creating and deleting at the same time."

msgid ""
"Fixed an issue that orphan volumes left after removing instances, especially "
"for the case that instance creation failed because of timeout when waiting "
"for the volume available."
msgstr ""
"Fixed an issue that orphan volumes left after removing instances, especially "
"for the case that instance creation failed because of timeout when waiting "
"for the volume available."

msgid ""
"Fixed an issue that the replication configuration is lost after resizing "
"instance."
msgstr ""
"Fixed an issue that the replication configuration is lost after resizing the "
"instance."

msgid ""
"Fixed backup creation failed issue when using custom container image "
"registry."
msgstr ""
"Fixed backup creation failed issue when using custom container image "
"registry."

msgid ""
"Fixed default configuration template for MySQL to ensure that replication "
"uses binlog_format. Bug 1563541."
msgstr ""
"Fixed default configuration template for MySQL to ensure that replication "
"uses binlog_format. Bug 1563541."

msgid ""
"Fixed issue where module-apply after module-remove caused module-query to "
"skip reporting on that module. Bug 1571799"
msgstr ""
"Fixed issue where module-apply after module-remove caused module-query to "
"skip reporting on that module. Bug 1571799"

msgid ""
"Fixed parsing of GTID references containing a list of GTIDs from "
"xtrabackup_binlog_info file on MySql replicas."
msgstr ""
"Fixed parsing of GTID references containing a list of GTIDs from "
"xtrabackup_binlog_info file on MySQL replicas."

msgid ""
"Fixed the issue that datastore version cannot be deleted because of "
"dependency of deleted instances. Now, when instance or backup is deleted, "
"the datastore version attribute is set to NULL in database. When datastore "
"configuration parameter is deleted, the record is deleted from database "
"rather than only set 'deleted' field to 1."
msgstr ""
"Fixed the issue that the datastore version cannot be deleted because of "
"dependency of deleted instances. When an instance or backup is deleted, the "
"datastore version attribute is set to NULL in the database. When the "
"datastore configuration parameter is deleted, the record is deleted from the "
"database rather than only set the 'deleted' field to 1."

msgid ""
"Fixed wrong call in conductor when reporting a guest notification exception. "
"Bug 1577848"
msgstr ""
"Fixed wrong call in conductor when reporting a guest notification exception. "
"Bug 1577848"

msgid ""
"Fixes an issue in galera_common shrink that wrong load removed nodes which "
"could missing a ClusterShrinkMustNotLeaveClusterEmpty exception or meet a "
"NotFound error. Bug 1699953"
msgstr ""
"Fixes an issue in galera_common shrink that the wrong load removed nodes "
"which could be missing a ClusterShrinkMustNotLeaveClusterEmpty exception or "
"meet a NotFound error. Bug 1699953"

msgid ""
"Fixes an issue with a failure to establish a new replica for MySQL in some "
"cases where a replica already exists and some data has been inserted into "
"the master. Bug 1563574"
msgstr ""
"Fixes an issue with a failure to establish a new replica for MySQL in some "
"cases where a replica already exists and some data has been inserted into "
"the master. Bug 1563574"

msgid ""
"Fixes an issue with redis configuration,it use a wrong min value for repl-"
"backlog-size in validation rules. Bug 1697596"
msgstr ""
"It fixes an issue with Redis configuration, it uses a wrong min value for "
"repl-backlog-size in validation rules. Bug 1697596"

msgid ""
"Fixes bug 1507841, provides a configuration setting to enable Role Based "
"Access Control (RBAC) for MongoDB clusters. If mongodb.cluster_secure is set "
"to False (default is True) then RBAC will be disabled."
msgstr ""
"It fixes bug 1507841, and provides a configuration setting to enable Role "
"Based Access Control (RBAC) for MongoDB clusters. If mongodb.cluster_secure "
"is set to False (default is True) then RBAC will be disabled."

msgid ""
"Fixes bug 1526024, a failure in growing a mongodb cluster because of a "
"problem in the way in which passwords were synchronized with new query "
"routers."
msgstr ""
"Fixes bug 1526024, a failure in growing a MongoDB cluster because of a "
"problem in the way in which passwords were synchronised with new query "
"routers."

msgid ""
"Fixes bug 1558794. The 2.3 version of Percona XtraBackup performs some "
"additional validations of the command line options passed to innobackupex. "
"The Trove code now complies with the new validations being performed."
msgstr ""
"Fixes bug 1558794. The 2.3 version of Percona XtraBackup performs some "
"additional validations of the command line options passed to innobackupex. "
"The Trove code now complies with the new validations being performed."

msgid ""
"For module ordering to work, db_upgrade must be run on the Trove database."
msgstr ""
"For module ordering to work, db_upgrade must be run on the Trove database."

msgid ""
"Full and incremental backup and restore strategy for postgres based on "
"pg_basebackup and WAL shipping."
msgstr ""
"Full and incremental backup and restore strategy for PostgreSQL based on "
"pg_basebackup and WAL shipping."

msgid "Generate trove events for the current period, and not a future period."
msgstr "Generate trove events for the current period and not a future period."

msgid ""
"If given, apply the configuration overrides in prepare, just before creating "
"initial users and/or databases. Failure to apply the given configuration "
"should flip the instance into a failed state. Default implementation saves "
"the overrides and restarts the database service to apply the changes. "
"Datastores that do not require restart may potentially override the base "
"implementation in 'apply_overrides_on_prepare()'."
msgstr ""
"If given, apply the configuration overrides in prepare, just before creating "
"initial users and/or databases. Failure to apply the given configuration "
"should flip the instance into a failed state. Default implementation saves "
"the overrides and restarts the database service to apply the changes. "
"Datastores that do not require restart may potentially override the base "
"implementation in 'apply_overrides_on_prepare()'."

msgid "Implement Postgres guestagent models for databases and users."
msgstr "Implement PostgreSQL guestagent models for databases and users."

msgid "Implement RootController extension for the Postgres datastore."
msgstr "Implement RootController extension for the PostgreSQL datastore."

msgid ""
"Implement configuration groups for Cassandra 2.1. You can now manage "
"configuration of Cassandra datastores using the Trove configuration groups "
"capability."
msgstr ""
"Implement configuration groups for Cassandra 2.1. You can now manage the "
"configuration of Cassandra datastores using the Trove configuration groups "
"capability."

msgid "Implemented configuration groups capability for Vertica datastores."
msgstr "Implemented configuration groups capability for Vertica datastores."

msgid ""
"Implemented grow and shrink for clusters of Vertica datastore. The number of "
"nodes in the cluster must be greater than the number required to satisfy the "
"min_ksafety configuration setting."
msgstr ""
"Implemented grow and shrink for clusters of Vertica datastore. The number of "
"nodes in the cluster must be greater than the number required to satisfy the "
"min_ksafety configuration setting."

msgid ""
"Implements replication based on GTIDs for MariaDB. Adds GTID replication "
"strategy for MariaDB. Implements MariaDB specific GTID handling in "
"guestagent. Configures MariaDB config template to support bin logging. Adds "
"MariaDB helper overrides to eliminate configuration group tests from "
"scenario tests."
msgstr ""
"Implements replication based on GTIDs for MariaDB. Adds GTID replication "
"strategy for MariaDB. Implements MariaDB specific GTID handling in "
"guestagent. Configures MariaDB config template to support bin logging. Adds "
"MariaDB helper overrides to eliminate configuration group tests from "
"scenario tests."

msgid ""
"Improved mountpoint detection by running it as root. This prevents guests "
"that have undiscoverable mount points from failing to unmount."
msgstr ""
"Improved mountpoint detection by running it as root. This prevents guests "
"that have undiscoverable mount points from failing to unmount."

msgid ""
"In Mitaka release, support was added for full offline backup and restore "
"using the default circular logging. In this release, the name of the "
"strategy for offline backup and restore was changed from DB2Backup to "
"DB2OfflineBackup. Hence, to enable offline backups, we should set "
"backup_strategy=DB2OfflineBackup and for online backups, "
"backup_strategy=DB2OnlineBackup. The property backup_namespace and "
"restore_namespace will be the same for both types of backup and restore."
msgstr ""
"In the Mitaka release, support was added for full offline backup and restore "
"using the default circular logging. In this release, the name of the "
"strategy for offline backup and restore was changed from DB2Backup to "
"DB2OfflineBackup. Hence, to enable offline backups, we should set "
"backup_strategy=DB2OfflineBackup and for online backups, "
"backup_strategy=DB2OnlineBackup. The property backup_namespace and "
"restore_namespace will be the same for both types of backup and restore."

msgid ""
"In multi-region deployment with geo-replicated Swift, the user can restore a "
"backup in one region by manually specifying the original backup data "
"location created in another region."
msgstr ""
"In a multi-region deployment with geo-replicated Swift, the user can restore "
"a backup in one region by manually specifying the original backup data "
"location created in another region."

msgid ""
"Increased agent_call_high_timeout config setting to 10 minutes.  This "
"configures the length of time that the taskmanager will wait for an "
"asynchronous guest agent call to complete."
msgstr ""
"Increased agent_call_high_timeout config setting to 10 minutes.  This "
"configures the length of time that the taskmanager will wait for an "
"asynchronous guest agent call to complete."

msgid "Make 'default_password_length' per-datastore-property. Bug 1572230"
msgstr "Make 'default_password_length' per-datastore-property. Bug 1572230"

msgid ""
"Make 'long query time' manageable via configuration groups (see bug "
"1542485). Deprecate the global 'guest_log_long_query_time' option in "
"preference of datastore-specific configurations. MySQL long_query_time "
"Percona long_query_time Percona XtraDB Cluster long_query_time MariaDB "
"long_query_time PostgreSQL log_min_duration_statement"
msgstr ""
"Make 'long query time' manageable via configuration groups (see bug "
"1542485). Deprecate the global 'guest_log_long_query_time' option in "
"preference of datastore-specific configurations. MySQL long_query_time "
"Percona long_query_time Percona XtraDB Cluster long_query_time MariaDB "
"long_query_time PostgreSQL log_min_duration_statement"

msgid ""
"Make guestagent reuse Cassandra connections to eliminate resource leaks. Bug "
"1566946."
msgstr ""
"Make guestagent reuse Cassandra connections to eliminate resource leaks. Bug "
"1566946."

msgid ""
"MariaDB allows an server to be a master and a slave simutaneously, so when "
"migrating masters, if the old master is reactivated before attaching the "
"other replicas to the new master, new unexpected GTIDs may be created on the "
"old master and synced to some of the other replicas by chance, as the other "
"replicas are still connecting to the old one by the time. After that these "
"diverged slave will fail changing to the new master. This will be fixed by "
"first attaching the other replicas to the new master, and then dealing with "
"old master. Fixes #1754539"
msgstr ""
"MariaDB allows a server to be a master and a slave simultaneously, so when "
"migrating masters, if the old master is reactivated before attaching the "
"other replicas to the new master, new unexpected GTIDs may be created on the "
"old master and synced to some of the other replicas by chance, as the other "
"replicas are still connecting to the old one by the time. After that these "
"diverged slaves will fail to change to the new master. This will be fixed by "
"first attaching the other replicas to the new master, and then dealing with "
"the old master. Fixes #1754539"

msgid ""
"MariaDB historically leveraged the mysql manager for guest support including "
"the configuration groups implementation. With MariaDB now having its own "
"manager class that inherits from Mysql, it needs to have validation_rules "
"and a ConfigParser setup. Bug 1532256"
msgstr ""
"MariaDB historically leveraged the MySQL manager for guest support including "
"the configuration groups implementation. With MariaDB now having its own "
"manager class that inherits from MySQL, it needs to have validation_rules "
"and a ConfigParser setup. Bug 1532256"

msgid "Mitaka Series Release Notes"
msgstr "Mitaka Series Release Notes"

msgid ""
"Module list/show now returns boolean values as True/False instead of 1/0.  "
"Bug 1656398"
msgstr ""
"Module list/show now returns boolean values as True/False instead of 1/0.  "
"Bug 1656398"

msgid ""
"Modules can now be applied in a consistent order, based on the new "
"'priority_apply' and 'apply_order' attributes when creating them. Blueprint "
"module-management-ordering"
msgstr ""
"Modules can now be applied in a consistent order, based on the new "
"'priority_apply' and 'apply_order' attributes when creating them. Blueprint "
"module-management-ordering"

msgid ""
"Mongo cluster grow operations were not creating instances with the provided "
"az and nic values. These should be used if the caller provided them."
msgstr ""
"Mongo cluster grow operations were not creating instances with the provided "
"az and nic values. These should be used if the caller provided them."

msgid ""
"Most of the options related to backup and restore are removed, e.g. "
"backup_namespace, restore_namespace, backup_incremental_strategy"
msgstr ""
"Most of the options related to backup and restore are removed, e.g. "
"backup_namespace, restore_namespace, backup_incremental_strategy"

msgid "New Features"
msgstr "New Features"

msgid ""
"New framework for ``trove-status upgrade check`` command is added. This "
"framework allows adding various checks which can be run before a Trove "
"upgrade to ensure if the upgrade can be performed safely."
msgstr ""
"New framework for ``trove-status upgrade check`` command is added. This "
"framework allows adding various checks which can be run before a Trove "
"upgrade to ensure if the upgrade can be performed safely."

msgid ""
"New instance upgrade API supports upgrading an instance of a datastore to a "
"new datastore version.  Includes implementation for MySQL family of "
"databases."
msgstr ""
"New instance upgrade API supports upgrading an instance of a datastore to a "
"new datastore version.  Includes implementation for MySQL family of "
"databases."

msgid ""
"New quota management APIs for reviewing and changing the quota for a "
"particular tenant.  Requires admin privileges."
msgstr ""
"New quota management APIs for reviewing and changing the quota for a "
"particular tenant.  Requires admin privileges."

msgid "Newton Series Release Notes"
msgstr "Newton Series Release Notes"

msgid "Ocata Series Release Notes"
msgstr "Ocata Series Release Notes"

msgid "Only one trove guest image is needed for all the datastores."
msgstr "Only one trove guest image is needed for all the datastores."

msgid ""
"OpenStack Trove now supports clustering for Cassandra datastores. You can "
"access clustering capabilities through the Trove cluster API."
msgstr ""
"OpenStack Trove now supports clustering for Cassandra datastores. You can "
"access clustering capabilities through the Trove cluster API."

msgid ""
"OpenStack Trove now supports clustering for MariaDB datastores. You can "
"access clustering capabilities through the Trove cluster API."
msgstr ""
"OpenStack Trove now supports clustering for MariaDB datastores. You can "
"access clustering capabilities through the Trove cluster API."

msgid ""
"OpenStack Trove now supports enable or disable authentication for Redis "
"datastore via the root-enable and root-disable API's."
msgstr ""
"OpenStack Trove now supports enable or disable authentication for Redis "
"datastore via the root-enable and root-disable API's."

msgid ""
"OpenStack Trove now supports superuser access for the Cassandra datastore "
"via the root-enable and root-disable API's."
msgstr ""
"OpenStack Trove now supports superuser access for the Cassandra datastore "
"via the root-enable and root-disable API's."

msgid ""
"Operator can now use new CLI tool ``trove-status upgrade check`` to check if "
"Trove deployment can be safely upgraded from N-1 to N release."
msgstr ""
"Operator can now use new CLI tool ``trove-status upgrade check`` to check if "
"Trove deployment can be safely upgraded from N-1 to N release."

msgid "Other Notes"
msgstr "Other Notes"

msgid ""
"Pass instance nic and az to cluster grow.  Add specific Fix for mongodb to "
"use the instance nic and az."
msgstr ""
"Pass instance nic and az to cluster grow.  Add specific Fix for MongoDB to "
"use the instance nic and az."

msgid ""
"Peviously root disable API returns a HTTP 200 response without any content, "
"a HTTP 204 reponse which is more appropriate will be returned now."
msgstr ""
"Previously root disable API returns a HTTP 200 response without any content, "
"a HTTP 204 response which is more appropriate will be returned now."

msgid "Pike Series Release Notes"
msgstr "Pike Series Release Notes"

msgid "Prelude"
msgstr "Prelude"

msgid ""
"Python 2.7 support has been dropped. Last release of Trove to support py2.7 "
"is OpenStack Train. The minimum version of Python now supported by Trove is "
"Python 3.6."
msgstr ""
"Python 2.7 support has been dropped. The last release of Trove to support "
"Python 2.7 was OpenStack Train. The minimum version of Python now supported "
"by Trove is Python 3.6."

msgid ""
"Python 3.6 & 3.7 support has been dropped. The minimum version of Python now "
"supported is Python 3.8."
msgstr ""
"Python 3.6 & 3.7 support has been dropped. The minimum version of Python now "
"supported is Python 3.8."

msgid "Queens Series Release Notes"
msgstr "Queens Series Release Notes"

msgid "Refactor the datastore guest manager code."
msgstr "Refactor the datastore guest manager code."

msgid ""
"Remove support of creating volume from Nova. The former configuration "
"\"use_nova_server_volume\" is not used any more, for creating volumes, "
"cinderclient will be always used. Fixes bug #1673408."
msgstr ""
"Remove support for creating volume from Nova. The former configuration "
"\"use_nova_server_volume\" is not used anymore, for creating volumes, "
"cinderclient will be always used. Fixes bug #1673408."

msgid "Remove unused 'override.config.template' files. Bug 1575852"
msgstr "Remove unused 'override.config.template' files. Bug 1575852"

msgid ""
"Removed the ``[database] idle_timeout`` and ``[DEFAULT] idle_timeout`` "
"options. These were all legacy aliases for ``[database] "
"connection_recycle_time``."
msgstr ""
"Removed the ``[database] idle_timeout`` and ``[DEFAULT] idle_timeout`` "
"options. These were all legacy aliases for ``[database] "
"connection_recycle_time``."

msgid ""
"Replace the deprecated 'myisam-recover' option with its newer counterpart "
"'myisam-recover-options'."
msgstr ""
"Replace the deprecated 'myisam-recover' option with its newer counterpart "
"'myisam-recover-options'."

msgid "Rocky Series Release Notes"
msgstr "Rocky Series Release Notes"

msgid "Security Issues"
msgstr "Security Issues"

msgid "Show network ID for the instance private address."
msgstr "Show network ID for the instance private address."

msgid ""
"Since SafeConfigParser is deprecated in Python version 3.2, Trove uses "
"ConfigParser for versions of Python >= 3.2 and SafeConfigParser for earlier "
"versions. (Bug 1618666)"
msgstr ""
"Since SafeConfigParser is deprecated in Python version 3.2, Trove uses "
"ConfigParser for versions of Python >= 3.2 and SafeConfigParser for earlier "
"versions. (Bug 1618666)"

msgid ""
"Starting with 1.0.0 osprofiler release config options needed for its "
"workability are consolidated inside osprofiler itself."
msgstr ""
"Starting with 1.0.0 osprofiler release config options needed for its "
"workability are consolidated inside osprofiler itself."

msgid "Stein Series Release Notes"
msgstr "Stein Series Release Notes"

msgid ""
"Support ``subnet_id`` and ``ip_address`` for creating instance. When "
"creating instance, trove will check the network conflicts between user's "
"network and the management network, additionally, the cloud admin is able to "
"define other reserved networks by configuring ``reserved_network_cidrs``."
msgstr ""
"Support ``subnet_id`` and ``ip_address`` for creating an instance. When "
"creating an instance, Trove will check the network conflicts between user's "
"network and the management network, additionally, the cloud admin is able to "
"define other reserved networks by configuring ``reserved_network_cidrs``."

msgid "Support attaching and detaching of configuration groups on clusters."
msgstr "Support attaching and detaching of configuration groups on clusters."

msgid ""
"Support db instance rebuild. The rebuild operation is mainly for Trove "
"upgrade, especially when the interface between Trove controller and guest "
"agent changes. After Trove controller is upgraded, the cloud administrator "
"needs to send rebuild request with the new guest image ID. Communication "
"with the end users is needed as the database service is offline during the "
"process. User's data in the database is not affected."
msgstr ""
"Support db instance rebuild. The rebuild operation is mainly for Trove "
"upgrades, especially when the interface between the Trove controller and "
"guest agent changes. After the Trove controller is upgraded, the cloud "
"administrator needs to send a rebuild request with the new guest image ID. "
"Communication with the end users is needed as the database service is "
"offline during the process. User's data in the database is not affected."

msgid ""
"Support for standard WAL based streaming replication for postgresql guests. "
"Sets up read-only hot standby servers."
msgstr ""
"Support for standard WAL based streaming replication for PostgreSQL guests. "
"Sets up read-only hot standby servers."

msgid ""
"Support for the new 'reapply' command. This allows a given module to be "
"reapplied to all instances that it had previously been applied to. Bug "
"1554903"
msgstr ""
"Support for the new 'reapply' command. This allows a given module to be "
"reapplied to all instances that it had previously been applied to. Bug "
"1554903"

msgid "Support for upgrading Redis cluster."
msgstr "Support for upgrading Redis cluster."

msgid "Support for upgrading Redis instance."
msgstr "Support for upgrading Redis instance."

msgid ""
"Support has been added for Cassandra backup and resture using the Nodetool "
"utility."
msgstr ""
"Support has been added for Cassandra backup and restore using the Nodetool "
"utility."

msgid "Support has been added for CouchDB Backup and Restore."
msgstr "Support has been added for CouchDB Backup and Restore."

msgid "Support has been added for CouchDB database and user functions."
msgstr "Support has been added for CouchDB database and user functions."

msgid "Support has been added for DB2 Express-C Backup and Restore."
msgstr "Support has been added for DB2 Express-C Backup and Restore."

msgid "Support has been added for Percona XtraBackup version 2.3."
msgstr "Support has been added for Percona XtraBackup version 2.3."

msgid ""
"Support image tags for the datastore version. When using image tags, Trove "
"is able to get the image dynamically from Glance for creating instances. If "
"both are specified, image ID takes precedence over image tags."
msgstr ""
"Support image tags for the datastore version. When using image tags, Trove "
"is able to get the image dynamically from Glance for creating instances. If "
"both are specified, image ID takes precedence over image tags."

msgid ""
"Support to filter backups by ``instance_id``, additionally, admin user can "
"get backups of all the projects by specifying ``all_projects`` in the query "
"string parameters."
msgstr ""
"Support to filter backups by ``instance_id``, additionally, the admin user "
"can get backups of all the projects by specifying ``all_projects`` in the "
"query string parameters."

msgid ""
"Support was added for applying modules to cluster instances through cluster-"
"create and cluster-grow. Bug 1578917"
msgstr ""
"Support was added for applying modules to cluster instances through cluster-"
"create and cluster-grow. Bug 1578917"

msgid ""
"The 'ip' field of getting instance response is deprecated and will be "
"removed in W release."
msgstr ""
"The 'ip' field of getting instance response is deprecated and will be "
"removed in W release."

msgid ""
"The --incremental flag for backup-create will add the ability to create "
"incremental backup based on last full or incremental backup. If no full or "
"incremental backup exists a new full backup will be created."
msgstr ""
"The --incremental flag for backup-create will add the ability to create "
"incremental backup based on last full or incremental backup. If no full or "
"incremental backup exists a new full backup will be created."

msgid ""
"The adds support for pxc to grow a cluster. * api and taskmanager support "
"for shrinking a cluster * validate that the networks given are the same for "
"each instance in the cluster. * make sure to add the existing networks on an "
"instance in the cluster. * add new Error task for grow and shrink. * nova "
"client version configuration changed to a string option rather than an int "
"option because the nova microversions change nova api output. This was "
"needed for the network interfaces on existing instances. * testing for grow "
"and shrink cluster"
msgstr ""
"This adds support for pxc to grow a cluster. * API and taskmanager support "
"for shrinking a cluster * validate that the networks given are the same for "
"each instance in the cluster. * make sure to add the existing networks on an "
"instance in the cluster. * add new Error task for grow and shrink. * Nova "
"client version configuration changed to a string option rather than an int "
"option because the Nova microversions change Nova API output. This was "
"needed for the network interfaces on existing instances. * testing for grow "
"and shrink cluster"

msgid "The admin user is able to get backups of a specific project."
msgstr "The admin user is able to get backups of a specific project."

msgid ""
"The cloud admin is able to apply a security group to management port(with "
"purpose of communicating with control plane and other management tasks) of "
"the Trove instance, by setting the ``management_security_groups`` config "
"option. The cloud admin is responsible for managing the security group "
"rules. The security group and its rules need to be created before deploying "
"Trove."
msgstr ""
"The cloud admin is able to apply a security group to the management "
"port(with the purpose of communicating with the control plane and other "
"management tasks) of the Trove instance, by setting the "
"``management_security_groups`` config option. The cloud admin is responsible "
"for managing the security group rules. The security group and its rules need "
"to be created before deploying Trove."

msgid ""
"The config option ``default_neutron_networks`` is deprecated and will be "
"removed in the future release, use ``management_networks`` instead."
msgstr ""
"The config option ``default_neutron_networks`` is deprecated and will be "
"removed in the future release, use ``management_networks`` instead."

msgid ""
"The database backup and restore operations are performed by docker container "
"inside the trove instance."
msgstr ""
"The database backup and restore operations are performed by the Docker "
"container inside the Trove instance."

msgid ""
"The default value of ``[oslo_policy] policy_file`` config option has been "
"changed from ``policy.json`` to ``policy.yaml``. Operators who are utilizing "
"customized or previously generated static policy JSON files (which are not "
"needed by default), should generate new policy files or convert them in YAML "
"format. Use the `oslopolicy-convert-json-to-yaml <https://docs.openstack.org/"
"oslo.policy/latest/cli/oslopolicy-convert-json-to-yaml.html>`_ tool to "
"convert a JSON to YAML formatted policy file in backward compatible way."
msgstr ""
"The default value of ``[oslo_policy] policy_file`` config option has been "
"changed from ``policy.json`` to ``policy.yaml``. Operators who are utilizing "
"customized or previously generated static policy JSON files (which are not "
"needed by default), should generate new policy files or convert them into "
"YAML format. Use the `oslopolicy-convert-json-to-yaml <https://docs."
"openstack.org/oslo.policy/latest/cli/oslopolicy-convert-json-to-yaml.html>`_ "
"tool to convert a JSON to YAML formatted policy file in a backward "
"compatible way."

msgid ""
"The default value of the trove guest agent config option ``[postgresql] "
"backup_docker_image`` is changed to ``openstacktrove/db-backup-"
"postgresql:1.1.1``. There is nothing to do if the option is not configured "
"explicitly."
msgstr ""
"The default value of the Trove guest agent config option ``[postgresql] "
"backup_docker_image`` is changed to ``openstacktrove/db-backup-"
"postgresql:1.1.1``. There is nothing to do if the option is not configured "
"explicitly."

msgid ""
"The following config options are deprecated in favor of a separate "
"configuration section ``service_credentials`` introduced to define the Trove "
"service user credentials for communication with other OpenStack services."
msgstr ""
"The following config options are deprecated in favour of a separate "
"configuration section ``service_credentials`` introduced to define the Trove "
"service user credentials for communication with other OpenStack services."

msgid ""
"The force-delete command will allow the deletion of an instance even if the "
"instance is stuck in BUILD state."
msgstr ""
"The force-delete command will allow the deletion of an instance even if the "
"instance is stuck in BUILD state."

msgid ""
"The guest log code raises a non-serializable exception if the given Swift "
"endpoint is invalid. This causes an ambiguous \"Circular reference detected"
"\" error on the guest, and a timeout on the caller. This case is now caught "
"and the correct exception raised."
msgstr ""
"The guest log code raises a non-serializable exception if the given Swift "
"endpoint is invalid. This causes an ambiguous \"Circular reference detected"
"\" error on the guest and a timeout on the caller. This case is now caught "
"and the correct exception raised."

msgid ""
"The management security group won't affect the Trove instances created "
"before upgrade."
msgstr ""
"The management security group won't affect the Trove instances created "
"before the upgrade."

msgid ""
"The minimum version of oslo.concurrency required has been changed from 3.5.0 "
"to 3.7.1"
msgstr ""
"The minimum version of oslo.concurrency required has been changed from 3.5.0 "
"to 3.7.1"

msgid ""
"The module-instances command now returns a paginated list of instances.  A --"
"count_only flag was added to the command to return a summary of the applied "
"instances based on the MD5 of the module (this is most useful for "
"live_update modules, to see which ones haven't been updated). Bug 1554900"
msgstr ""
"The module-instances command now returns a paginated list of instances.  A --"
"count_only flag was added to the command to return a summary of the applied "
"instances based on the MD5 of the module (this is most useful for "
"live_update modules, to see which ones haven't been updated). Bug 1554900"

msgid ""
"The payload for cluster GET now returns ips for all networks, not just the "
"first one found for each instance. Bug 1642695"
msgstr ""
"The payload for cluster GET now returns IPs for all networks, not just the "
"first one found for each instance. Bug 1642695"

msgid "The project user can query the project's own resource quota."
msgstr "The project user can query the project's own resource quota."

msgid ""
"The reset-status command will set the task and status of an instance to "
"ERROR after which it can be deleted."
msgstr ""
"The reset-status command will set the task and status of an instance to "
"ERROR after which it can be deleted."

msgid "The support of Bionic has been removed."
msgstr "The support of Bionic has been removed."

msgid ""
"The user can create backup strategy to define the configurations for "
"creating backups, e.g. the swift container to store the backup data. Users "
"can also specify the container name when creating backups which takes "
"precedence over the backup strategy configuration."
msgstr ""
"The user can create a backup strategy to define the configurations for "
"creating backups, e.g. the swift container to store the backup data. Users "
"can also specify the container name when creating backups which take "
"precedence over the backup strategy configuration."

msgid ""
"This patch set implements the following functionality for Cassandra "
"datastore. create/delete/get user list users change password grant/revoke/"
"list access update attributes create/delete database list databases Notes on "
"Cassandra users In Cassandra only SUPERUSERS can create other users and "
"grant permissions to database resources. Trove uses the 'os_admin' superuser "
"to perform its administrative tasks. It proactively removes the built-in "
"'cassandra' superuser on prepare. The users it creates are all "
"'normal' (NOSUPERUSER) accounts. The permissions it can grant are also "
"limited to non-superuser operations. This is to prevent anybody from "
"creating a new superuser via the Trove API. Updatable attributes include "
"username and password. The configuration template had to be updated to "
"enable authentication and authorization support (original configuration "
"allowed anonymous connections). Default implementations used are "
"authenticator org.apache.cassandra.auth.PasswordAuthenticator authorizer org."
"apache.cassandra.auth.CassandraAuthorizer The superuser password is set to a "
"random Trove password which is then stored in a Trove-read-only file in '~/."
"cassandra/cqlshrc' which is also the default location for client settings. "
"Notes on Cassandra keyspaces Cassandra stores replicas on multiple nodes to "
"ensure reliability and fault tolerance. All replicas are equally important; "
"there is no primary or master. A replication strategy determines the nodes "
"where replicas are placed. The total number of replicas across the cluster "
"is referred to as the replication factor. The above 'create database' "
"implementation uses 'SimpleStrategy' with just a single replica on the guest "
"machine. This is a very simplistic configuration only good for the most "
"basic applications and demonstration purposes. SimpleStrategy is for a "
"single data center only. The following system keyspaces have been included "
"in the default 'ignore_dbs' configuration list and therefore excluded from "
"all database operations 'system', 'system_auth', 'system_traces' Notes on "
"user rename Cassandra does not have a native way for renaming users. The "
"reason why Cassandra itself does not implement rename is apparently just "
"lack of demand for that feature. We implement it by creating a new user, "
"transferring permissions and dropping the old one (which also removes its "
"existing permissions). I asked about the sanity of this rename approach on "
"the Cassandra mailing list and IRC channel and there should not be anything "
"inherently wrong with the proposed procedure. This method, however, requires "
"the user to always provide a password. Additional notes Trove uses the "
"official open-source Python driver for Cassandra to connect to the database "
"and execute queries. The connection is implemented in CassandraConnection. "
"It is now also used to obtain the current database status as opposed to the "
"original method of parsing output of the client tool. The 'common/"
"operating_system' module was extended with two new functions for reading/"
"writing ini-style and YAML configuration files to/from Python dicts. Unit "
"tests were added to 'guestagent/test_operating_system'. The existing Manager "
"unit tests were extended to include the added functionality. Also includes "
"some minor improvements to comments and log messages. Used the existing "
"operating_system interface to update file ownership. The system module was "
"removed and its contents moved to the Application class. This is to reduce "
"the number of files and help facilitate overriding."
msgstr ""
"This patch set implements the following functionality for Cassandra "
"datastore. create/delete/get user list users change password grant/revoke/"
"list access update attributes create/delete database list databases Notes on "
"Cassandra users In Cassandra only SUPERUSERS can create other users and "
"grant permissions to database resources. Trove uses the 'os_admin' superuser "
"to perform its administrative tasks. It proactively removes the built-in "
"'cassandra' superuser on prepare. The users it creates are all "
"'normal' (NOSUPERUSER) accounts. The permissions it can grant are also "
"limited to non-superuser operations. This is to prevent anybody from "
"creating a new superuser via the Trove API. Updatable attributes include "
"username and password. The configuration template had to be updated to "
"enable authentication and authorization support (original configuration "
"allowed anonymous connections). Default implementations used are "
"authenticator org.apache.cassandra.auth.PasswordAuthenticator authorizer org."
"apache.cassandra.auth.CassandraAuthorizer The superuser password is set to a "
"random Trove password which is then stored in a Trove-read-only file in '~/."
"cassandra/cqlshrc' which is also the default location for client settings. "
"Notes on Cassandra keyspaces Cassandra stores replicas on multiple nodes to "
"ensure reliability and fault tolerance. All replicas are equally important; "
"there is no primary or master. A replication strategy determines the nodes "
"where replicas are placed. The total number of replicas across the cluster "
"is referred to as the replication factor. The above 'create database' "
"implementation uses 'SimpleStrategy' with just a single replica on the guest "
"machine. This is a very simplistic configuration only good for the most "
"basic applications and demonstration purposes. SimpleStrategy is for a "
"single data centre only. The following system keyspaces have been included "
"in the default 'ignore_dbs' configuration list and therefore excluded from "
"all database operations 'system', 'system_auth', 'system_traces' Notes on "
"user rename Cassandra does not have a native way for renaming users. The "
"reason why Cassandra itself does not implement rename is apparently just a "
"lack of demand for that feature. We implement it by creating a new user, "
"transferring permissions and dropping the old one (which also removes its "
"existing permissions). I asked about the sanity of this rename approach on "
"the Cassandra mailing list and IRC channel and there should not be anything "
"inherently wrong with the proposed procedure. This method, however, requires "
"the user to always provide a password. Additional notes Trove uses the "
"official open-source Python driver for Cassandra to connect to the database "
"and execute queries. The connection is implemented in CassandraConnection. "
"It is now also used to obtain the current database status as opposed to the "
"original method of parsing output of the client tool. The 'common/"
"operating_system' module was extended with two new functions for reading/"
"writing ini-style and YAML configuration files to/from Python dicts. Unit "
"tests were added to 'guestagent/test_operating_system'. The existing Manager "
"unit tests were extended to include the added functionality. Also includes "
"some minor improvements to comments and log messages. Used the existing "
"operating_system interface to update file ownership. The system module was "
"removed and its contents moved to the Application class. This is to reduce "
"the number of files and help facilitate overriding."

msgid ""
"This would apply to any type of cluster that uses the galera strategy while "
"setting the nics on a create call. When we called cast to set() the object "
"was a list of lists. The set method can not has a list so this was causesing "
"a unhashable error. The change is to make the instance_nics a list of "
"strings (what we originaly expected) to resolve this issue. Bug 1570602."
msgstr ""
"This would apply to any type of cluster that uses the Galera strategy while "
"setting the nics on a create call. When we called cast to set() the object "
"was a list of lists. The set method can not has a list so this was causing "
"an unhashable error. The change is to make the instance_nics a list of "
"strings (what we originally expected) to resolve this issue. Bug 1570602."

msgid "Train Series Release Notes"
msgstr "Train Series Release Notes"

msgid "Trove Release Notes"
msgstr "Trove Release Notes"

msgid ""
"Trove admin user is able to remove the default configuration parameters for "
"datastore version, e.g. ``trove-manage db_remove_datastore_config_parameters "
"mysql 5.7.29``"
msgstr ""
"The Trove admin user is able to remove the default configuration parameters "
"for datastore version, e.g. ``trove-manage "
"db_remove_datastore_config_parameters mysql 5.7.29``"

msgid ""
"Trove is now using admin clients by default to communicate with Nova, "
"Cinder, Neutron and Glance. Deployers want to stick to the old clients need "
"to explicitly config the following options:"
msgstr ""
"Trove is now using admin clients by default to communicate with Nova, "
"Cinder, Neutron and Glance. Deployers want to stick to the old clients need "
"to explicitly config the following options:"

msgid ""
"Trove now publishes images of some specific databases on http://tarballs."
"openstack.org/trove/images/ for testing purpose."
msgstr ""
"Trove now publishes images of some specific databases on http://tarballs."
"openstack.org/trove/images/ for testing purpose."

msgid ""
"Trove now supports to resize volume without downtime. To use this feature, "
"the version of Nova and Cinder needs to be at least Pike, the config option "
"``cinder_service_type`` needs to be set to ``volumev3``. The cloud admin can "
"disable this feature by setting ``online_volume_resize=False``, default is "
"enabled."
msgstr ""
"Trove now supports to resize volume without downtime. To use this feature, "
"the version of Nova and Cinder needs to be at least Pike, the config option "
"``cinder_service_type`` needs to be set to ``volumev3``. The cloud admin can "
"disable this feature by setting ``online_volume_resize=False``, default is "
"enabled."

msgid ""
"Updating a module with all_datastores and all_datastore_versions now works "
"correctly. Bug 1612430"
msgstr ""
"Updating a module with all_datastores and all_datastore_versions now works "
"correctly. Bug 1612430"

msgid "Upgrade Notes"
msgstr "Upgrade Notes"

msgid "Use SET PASSWORD and RENAME USER queries to update user properties."
msgstr "Use SET PASSWORD and RENAME USER queries to update user properties."

msgid ""
"Use of JSON policy files was deprecated by the ``oslo.policy`` library "
"during the Victoria development cycle. As a result, this deprecation is "
"being noted in the Wallaby cycle with an anticipated future removal of "
"support by ``oslo.policy``. As such operators will need to convert to YAML "
"policy files. Please see the upgrade notes for details on migration of any "
"custom policy files."
msgstr ""
"Use of JSON policy files was deprecated by the ``oslo.policy`` library "
"during the Victoria development cycle. As a result, this deprecation is "
"being noted in the Wallaby cycle with an anticipated future removal of "
"support by ``oslo.policy``. As such operators will need to convert to YAML "
"policy files. Please see the upgrade notes for details on the migration of "
"any custom policy files."

msgid ""
"User can specify the number and volume of mongos/configserver with "
"extended_properties argument when creating mongodb cluster. Currently, the "
"supported parameters are, num_configsvr, num_mongos, configsvr_volume_size, "
"configsvr_volume_type, mongos_volume_size and mongos_volume_type."
msgstr ""
"User can specify the number and volume of mongos/configserver with "
"extended_properties argument when creating mongodb cluster. Currently, the "
"supported parameters are, num_configsvr, num_mongos, configsvr_volume_size, "
"configsvr_volume_type, mongos_volume_size and mongos_volume_type."

msgid ""
"Users can create ``public`` trove instance that has a floating IP attached "
"but have the ability to define what CIDRs could access the user's database "
"service. Refer to the `API doc <https://developer.openstack.org/api-ref/"
"database/>`_ for more details."
msgstr ""
"Users can create ``public`` trove instance that has a floating IP attached "
"but have the ability to define what CIDRs could access the user's database "
"service. Refer to the `API doc <https://developer.openstack.org/api-ref/"
"database/>`_ for more details."

msgid "Ussuri Series Release Notes"
msgstr "Ussuri Series Release Notes"

msgid ""
"Vertica comes with a User Defined Load function that takes a URL as a load "
"source. This can be used to load files that are stored in Swift. As this is "
"a common use case, it is valuable to enable this by default. This can be "
"done in the post-prepare method for Vertica. A new UDL_LIBS list has been "
"added that describes any UDLs to be loaded into the database. This change "
"only has one entry - the curl function."
msgstr ""
"Vertica comes with a User Defined Load (UDL) function that takes a URL as a "
"load source. This can be used to load files that are stored in Swift. As "
"this is a common use case, it is valuable to enable this by default. This "
"can be done in the post-prepare method for Vertica. A new UDL_LIBS list has "
"been added that describes any UDLs to be loaded into the database. This "
"change only has one entry - the curl function."

msgid "Victoria Series Release Notes"
msgstr "Victoria Series Release Notes"

msgid "Wallaby Series Release Notes"
msgstr "Wallaby Series Release Notes"

msgid ""
"When resizing volume for an instance which is the primary of a replication "
"cluster, Trove also resizes the volume for all the replicas automatically."
msgstr ""
"When resizing volume for an instance which is the primary of a replication "
"cluster, Trove also resizes the volume for all the replicas automatically."

msgid ""
"When the trove-guestagent failed to update the datastore service status, the "
"instance status should be ERROR."
msgstr ""
"When the trove-guestagent failed to update the datastore service status, the "
"instance status should be ERROR."

msgid "Xena Series Release Notes"
msgstr "Xena Series Release Notes"

msgid "Yoga Series Release Notes"
msgstr "Yoga Series Release Notes"

msgid "Zed Series Release Notes"
msgstr "Zed Series Release Notes"

msgid ""
"[`bug 1776229 <https://bugs.launchpad.net/trove/+bug/1776229>`_] "
"endpoint_type is now working with single tenant which gives the possibility "
"to override the publicURL value."
msgstr ""
"[`bug 1776229 <https://bugs.launchpad.net/trove/+bug/1776229>`_] "
"endpoint_type is now working with single tenant which gives the possibility "
"to override the publicURL value."

msgid ""
"check if the user input is legal, currently, trove may have a  RCE "
"vulnerability. more details see: `Stroy 2010004 <https://storyboard."
"openstack.org/#!/story/2010004>`__"
msgstr ""
"check if the user input is legal, currently, Trove may have a  RCE "
"vulnerability. For more details see: `Stroy 2010004 <https://storyboard."
"openstack.org/#!/story/2010004>`__"

msgid "explicitly specify utf8mb3 as character set for mysql"
msgstr "explicitly specify utf8mb3 as the character set for MySQL"

msgid "remote_cinder_client"
msgstr "remote_cinder_client"

msgid "remote_glance_client"
msgstr "remote_glance_client"

msgid "remote_neutron_client"
msgstr "remote_neutron_client"

msgid "remote_nova_client"
msgstr "remote_nova_client"
