# The order of packages is significant, because pip processes them in the order
# of appearance. Changing the order has an impact on the overall integration
# process, which may cause wedges in the gate later.
# Hacking already pins down pep8, pyflakes and flake8
hacking>=3.0.1,<3.1.0 # Apache-2.0
bandit[baseline]>=1.7.7 # Apache-2.0
coverage!=4.4,>=4.0 # Apache-2.0
nose>=1.3.7 # LGPL
nosexcover>=1.0.10 # BSD
openstack.nose-plugin>=0.7 # Apache-2.0
WebTest>=2.0.27 # MIT
wsgi-intercept>=1.4.1 # MIT License
proboscis>=******* # Apache-2.0
python-troveclient>=2.2.0 # Apache-2.0
testtools>=2.2.0 # MIT
pymongo!=3.1,>=3.0.2 # Apache-2.0
redis>=2.10.0 # MIT
cassandra-driver!=3.6.0,>=2.1.4 # Apache-2.0
couchdb>=0.8 # Apache-2.0
stestr>=1.1.0 # Apache-2.0
doc8>=0.8.1 # Apache-2.0
astroid==1.6.5 # LGPLv2.1
pylint==1.9.2 # GPLv2
oslotest>=3.2.0 # Apache-2.0
tenacity>=4.9.0  # Apache-2.0
# Docs building
openstackdocstheme>=2.2.1 # Apache-2.0
os-api-ref>=1.4.0 # Apache-2.0
reno>=3.1.0 # Apache-2.0
